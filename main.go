package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"sort"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/fatih/color"
	"github.com/schollz/progressbar/v3"
)

// TestConfig 测试配置
type TestConfig struct {
	Name     string            `json:"name"`
	URL      string            `json:"url"`
	JSONFile string            `json:"json_file"`
	QPS      int               `json:"qps"`
	Duration int               `json:"duration"`
	Headers  map[string]string `json:"headers"`
}

// TestResult 测试结果
type TestResult struct {
	Name            string        `json:"name"`
	TotalRequests   int64         `json:"total_requests"`
	SuccessRequests int64         `json:"success_requests"`
	FailedRequests  int64         `json:"failed_requests"`
	TotalDuration   time.Duration `json:"total_duration"`
	ActualQPS       float64       `json:"actual_qps"`
	AvgLatency      time.Duration `json:"avg_latency"`
	P95Latency      time.Duration `json:"p95_latency"`
	P99Latency      time.Duration `json:"p99_latency"`
	StatusCodes     map[int]int64 `json:"status_codes"`
	Errors          []string      `json:"errors"`
}

// RequestResult 单个请求结果
type RequestResult struct {
	StatusCode int
	Latency    time.Duration
	Error      error
	Timestamp  time.Time
}

// QpsController 精确QPS控制器
type QpsController struct {
	rate       float64
	interval   time.Duration
	ticker     *time.Ticker
	tokens     chan struct{}
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
}

// NewQpsController 创建QPS控制器
func NewQpsController(qps float64) *QpsController {
	ctx, cancel := context.WithCancel(context.Background())
	
	controller := &QpsController{
		rate:     qps,
		interval: time.Duration(float64(time.Second) / qps),
		tokens:   make(chan struct{}, int(qps*2)), // 缓冲区
		ctx:      ctx,
		cancel:   cancel,
	}
	
	// 启动令牌生成器
	controller.wg.Add(1)
	go controller.tokenGenerator()
	
	return controller
}

// tokenGenerator 令牌生成器
func (qc *QpsController) tokenGenerator() {
	defer qc.wg.Done()
	
	qc.ticker = time.NewTicker(qc.interval)
	defer qc.ticker.Stop()
	
	for {
		select {
		case <-qc.ctx.Done():
			return
		case <-qc.ticker.C:
			// 非阻塞发送令牌
			select {
			case qc.tokens <- struct{}{}:
			default:
				// 桶满了，丢弃令牌
			}
		}
	}
}

// Acquire 获取令牌
func (qc *QpsController) Acquire() {
	<-qc.tokens
}

// Close 关闭控制器
func (qc *QpsController) Close() {
	qc.cancel()
	qc.wg.Wait()
	close(qc.tokens)
}

// QpsTester 高性能QPS测试器
type QpsTester struct {
	client     *http.Client
	controller *QpsController
}

// NewQpsTester 创建QPS测试器
func NewQpsTester(qps float64, timeout time.Duration) *QpsTester {
	return &QpsTester{
		client: &http.Client{
			Timeout: timeout,
			Transport: &http.Transport{
				MaxIdleConns:        1000,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
			},
		},
		controller: NewQpsController(qps),
	}
}

// RunTest 运行QPS测试
func (qt *QpsTester) RunTest(config TestConfig) (*TestResult, error) {
	// 读取JSON文件
	jsonBody, err := qt.loadJSONFile(config.JSONFile)
	if err != nil {
		return nil, fmt.Errorf("加载JSON文件失败: %v", err)
	}
	
	startTime := time.Now()
	endTime := startTime.Add(time.Duration(config.Duration) * time.Second)
	
	var results []*RequestResult
	var resultsMutex sync.Mutex
	var wg sync.WaitGroup
	requestCount := int64(0)
	
	// 创建进度条
	bar := progressbar.NewOptions64(
		int64(config.QPS*config.Duration),
		progressbar.OptionSetDescription(fmt.Sprintf("[%s] 发送请求", config.Name)),
		progressbar.OptionSetWidth(50),
		progressbar.OptionShowCount(),
		progressbar.OptionShowIts(),
		progressbar.OptionSetItsString("req"),
	)
	
	// 启动请求发送器
	for time.Now().Before(endTime) {
		qt.controller.Acquire() // 获取令牌
		
		wg.Add(1)
		go func() {
			defer wg.Done()
			result := qt.sendRequest(config, jsonBody)
			
			resultsMutex.Lock()
			results = append(results, result)
			resultsMutex.Unlock()
			
			atomic.AddInt64(&requestCount, 1)
			bar.Add(1)
		}()
	}
	
	// 等待所有请求完成
	wg.Wait()
	bar.Finish()
	
	// 计算统计结果
	return qt.calculateResults(config.Name, results, time.Since(startTime), requestCount), nil
}

// loadJSONFile 加载JSON文件
func (qt *QpsTester) loadJSONFile(filename string) (string, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return "", err
	}
	
	// 验证JSON格式
	var jsonData interface{}
	if err := json.Unmarshal(data, &jsonData); err != nil {
		return "", fmt.Errorf("无效的JSON格式: %v", err)
	}
	
	return string(data), nil
}

// sendRequest 发送单个请求
func (qt *QpsTester) sendRequest(config TestConfig, jsonBody string) *RequestResult {
	start := time.Now()
	
	req, err := http.NewRequest("POST", config.URL, strings.NewReader(jsonBody))
	if err != nil {
		return &RequestResult{
			Error:     err,
			Timestamp: start,
			Latency:   time.Since(start),
		}
	}
	
	// 设置请求头
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}
	
	resp, err := qt.client.Do(req)
	latency := time.Since(start)
	
	result := &RequestResult{
		Latency:   latency,
		Timestamp: start,
	}
	
	if err != nil {
		result.Error = err
	} else {
		result.StatusCode = resp.StatusCode
		io.Copy(io.Discard, resp.Body) // 读取并丢弃响应体
		resp.Body.Close()
	}
	
	return result
}

// calculateResults 计算测试结果
func (qt *QpsTester) calculateResults(name string, results []*RequestResult, totalDuration time.Duration, requestCount int64) *TestResult {
	if len(results) == 0 {
		return &TestResult{Name: name}
	}
	
	var successCount, failedCount int64
	var totalLatency time.Duration
	statusCodes := make(map[int]int64)
	var errors []string
	var latencies []time.Duration
	
	for _, result := range results {
		if result.Error != nil {
			failedCount++
			errors = append(errors, result.Error.Error())
		} else {
			successCount++
			statusCodes[result.StatusCode]++
		}
		
		totalLatency += result.Latency
		latencies = append(latencies, result.Latency)
	}
	
	// 计算延迟百分位数
	p95, p99 := calculatePercentiles(latencies)
	
	return &TestResult{
		Name:            name,
		TotalRequests:   requestCount,
		SuccessRequests: successCount,
		FailedRequests:  failedCount,
		TotalDuration:   totalDuration,
		ActualQPS:       float64(requestCount) / totalDuration.Seconds(),
		AvgLatency:      time.Duration(int64(totalLatency) / int64(len(results))),
		P95Latency:      p95,
		P99Latency:      p99,
		StatusCodes:     statusCodes,
		Errors:          errors,
	}
}

// calculatePercentiles 计算延迟百分位数
func calculatePercentiles(latencies []time.Duration) (p95, p99 time.Duration) {
	if len(latencies) == 0 {
		return 0, 0
	}
	
	sort.Slice(latencies, func(i, j int) bool {
		return latencies[i] < latencies[j]
	})
	
	p95Index := int(float64(len(latencies)) * 0.95)
	p99Index := int(float64(len(latencies)) * 0.99)
	
	if p95Index >= len(latencies) {
		p95Index = len(latencies) - 1
	}
	if p99Index >= len(latencies) {
		p99Index = len(latencies) - 1
	}
	
	return latencies[p95Index], latencies[p99Index]
}

// Close 关闭测试器
func (qt *QpsTester) Close() {
	qt.controller.Close()
}

func main() {
	// 测试配置
	configs := []TestConfig{
		{
			Name:     "banner-app1",
			URL:      "http://172.31.236.166:8089/engine/bid/transsion",
			JSONFile: "banner-app1.json",
			QPS:      50,
			Duration: 2,
			Headers:  map[string]string{"Content-Type": "application/json"},
		},
		{
			Name:     "banner-app2",
			URL:      "http://172.31.236.166:8089/engine/bid/transsion",
			JSONFile: "banner-app2.json",
			QPS:      20,
			Duration: 2,
			Headers:  map[string]string{"Content-Type": "application/json"},
		},
		{
			Name:     "native-app1",
			URL:      "http://172.31.236.166:8089/engine/bid/transsion",
			JSONFile: "native-app1.json",
			QPS:      120,
			Duration: 2,
			Headers:  map[string]string{"Content-Type": "application/json"},
		},
		{
			Name:     "native-app2",
			URL:      "http://172.31.236.166:8089/engine/bid/transsion",
			JSONFile: "native-app2.json",
			QPS:      50,
			Duration: 2,
			Headers:  map[string]string{"Content-Type": "application/json"},
		},
	}
	
	// 计算总QPS
	totalQPS := 0
	for _, config := range configs {
		totalQPS += config.QPS
	}
	
	color.Green("🚀 开始Go原生并行QPS测试")
	color.Cyan("目标总QPS: %d", totalQPS)
	fmt.Println()
	
	start := time.Now()
	
	// 并行执行测试
	var wg sync.WaitGroup
	results := make([]*TestResult, len(configs))
	
	for i, config := range configs {
		wg.Add(1)
		go func(index int, cfg TestConfig) {
			defer wg.Done()
			
			tester := NewQpsTester(float64(cfg.QPS), 10*time.Second)
			defer tester.Close()
			
			result, err := tester.RunTest(cfg)
			if err != nil {
				color.Red("❌ 测试 %s 失败: %v", cfg.Name, err)
				return
			}
			
			results[index] = result
		}(i, config)
	}
	
	// 等待所有测试完成
	wg.Wait()
	
	totalDuration := time.Since(start)
	
	// 计算总体统计
	var totalRequests, totalSuccess int64
	var totalActualQPS float64
	
	fmt.Println()
	color.Yellow("=== Go原生测试完成统计 ===")
	
	for _, result := range results {
		if result != nil {
			totalRequests += result.TotalRequests
			totalSuccess += result.SuccessRequests
			totalActualQPS += result.ActualQPS
			
			successRate := float64(result.SuccessRequests) / float64(result.TotalRequests) * 100
			color.Green("%s: 实际QPS=%.2f, 请求数=%d, 成功率=%.2f%%, P95延迟=%v",
				result.Name, result.ActualQPS, result.TotalRequests, successRate, result.P95Latency)
		}
	}
	
	fmt.Println()
	color.Cyan("总耗时: %v", totalDuration)
	color.Cyan("总请求数: %d", totalRequests)
	color.Cyan("实际总QPS: %.2f", totalActualQPS)
	if totalRequests > 0 {
		color.Cyan("总成功率: %.2f%%", float64(totalSuccess)/float64(totalRequests)*100)
	}
}
