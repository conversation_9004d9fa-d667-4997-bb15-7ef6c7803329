#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Vegeta集成测试脚本
用于验证Vegeta集成是否正常工作
"""

import asyncio
import sys
from pathlib import Path
from vegeta_integration import VegetaIntegration, SimpleTestConfig


async def test_vegeta_installation():
    """测试Vegeta是否正确安装"""
    print("🔍 检查Vegeta安装状态...")
    try:
        vegeta = VegetaIntegration()
        print("✅ Vegeta安装正常")
        return True
    except RuntimeError as e:
        print(f"❌ Vegeta安装检查失败: {e}")
        return False


async def test_json_files():
    """测试JSON文件是否存在"""
    print("\n🔍 检查JSON文件...")
    
    json_files = [
        "example-banner-request.json",
        "example-native-request.json",
        "transsion-banner-app-country.json",
        "transsion-native-app-country.json"
    ]
    
    available_files = []
    for json_file in json_files:
        if Path(json_file).exists():
            print(f"✅ 找到文件: {json_file}")
            available_files.append(json_file)
        else:
            print(f"⚠️  文件不存在: {json_file}")
    
    return available_files


async def test_single_request():
    """测试单个请求"""
    print("\n🔍 执行单个请求测试...")
    
    # 使用示例JSON文件
    config = SimpleTestConfig(
        name="test-single",
        url="https://httpbin.org/post",  # 使用公共测试API
        json_file="example-banner-request.json",
        qps=5,
        duration=3
    )
    
    try:
        vegeta = VegetaIntegration()
        result = await vegeta.run_simple_test(config)
        
        print(f"✅ 单个请求测试成功:")
        print(f"   实际QPS: {result.rate:.2f}")
        print(f"   总请求数: {result.requests}")
        print(f"   成功率: {result.success:.2%}")
        print(f"   平均延迟: {result.latencies['mean']:.2f}ms")
        print(f"   P95延迟: {result.latencies['p95']:.2f}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ 单个请求测试失败: {e}")
        return False


async def test_parallel_requests():
    """测试并行请求"""
    print("\n🔍 执行并行请求测试...")
    
    configs = [
        SimpleTestConfig(
            name="parallel-test-1",
            url="https://httpbin.org/post",
            json_file="example-banner-request.json",
            qps=3,
            duration=2
        ),
        SimpleTestConfig(
            name="parallel-test-2", 
            url="https://httpbin.org/post",
            json_file="example-native-request.json",
            qps=2,
            duration=2
        )
    ]
    
    try:
        vegeta = VegetaIntegration()
        
        # 并行执行
        tasks = [vegeta.run_simple_test(config) for config in configs]
        results = await asyncio.gather(*tasks)
        
        print(f"✅ 并行请求测试成功:")
        total_requests = sum(result.requests for result in results)
        total_qps = sum(result.rate for result in results)
        
        for i, (config, result) in enumerate(zip(configs, results)):
            print(f"   {config.name}: QPS={result.rate:.2f}, 请求数={result.requests}")
        
        print(f"   总QPS: {total_qps:.2f}")
        print(f"   总请求数: {total_requests}")
        
        return True
        
    except Exception as e:
        print(f"❌ 并行请求测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始Vegeta集成测试\n")
    
    # 测试步骤
    tests = [
        ("Vegeta安装检查", test_vegeta_installation),
        ("JSON文件检查", test_json_files),
        ("单个请求测试", test_single_request),
        ("并行请求测试", test_parallel_requests)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行测试: {test_name}")
        print('='*50)
        
        try:
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 总结
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！Vegeta集成工作正常")
        print("\n📝 下一步:")
        print("1. 准备你的JSON请求文件")
        print("2. 修改main.py中的URL和配置")
        print("3. 运行: python main.py vegeta")
    else:
        print("⚠️  部分测试失败，请检查配置")
        print("\n🔧 故障排除:")
        print("1. 确保Vegeta已正确安装: brew install vegeta")
        print("2. 检查JSON文件是否存在且格式正确")
        print("3. 确保网络连接正常")
    
    return passed == total


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
