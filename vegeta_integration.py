#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Vegeta集成方案 - 使用Go的Vegeta工具进行高性能QPS测试
"""

import asyncio
import json
import subprocess
import tempfile
import os
from typing import List, Dict, Any
from dataclasses import dataclass


@dataclass
class VegetaResult:
    """Vegeta测试结果"""
    latencies: Dict[str, float]
    bytes_in: Dict[str, int]
    bytes_out: Dict[str, int]
    duration: float
    requests: int
    rate: float
    throughput: float
    success: float
    status_codes: Dict[str, int]
    errors: List[str]


class VegetaIntegration:
    """Vegeta集成类"""
    
    def __init__(self, vegeta_path: str = "vegeta"):
        self.vegeta_path = vegeta_path
        self._check_vegeta_installation()
    
    def _check_vegeta_installation(self):
        """检查Vegeta是否已安装"""
        try:
            result = subprocess.run([self.vegeta_path, "-version"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                raise RuntimeError("Vegeta未正确安装")
        except FileNotFoundError:
            raise RuntimeError("未找到Vegeta，请先安装: brew install vegeta")
    
    async def run_qps_test(self, 
                          url: str,
                          method: str = "GET",
                          headers: Dict[str, str] = None,
                          body: str = None,
                          qps: int = 100,
                          duration: int = 10) -> VegetaResult:
        """
        运行QPS测试
        
        Args:
            url: 目标URL
            method: HTTP方法
            headers: 请求头
            body: 请求体
            qps: 目标QPS
            duration: 持续时间（秒）
            
        Returns:
            VegetaResult: 测试结果
        """
        # 创建临时目标文件
        target_content = self._build_target(url, method, headers, body)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(target_content)
            target_file = f.name
        
        try:
            # 运行Vegeta攻击
            attack_cmd = [
                self.vegeta_path, "attack",
                "-targets", target_file,
                "-rate", f"{qps}",
                "-duration", f"{duration}s",
                "-format", "http"
            ]
            
            # 执行攻击并生成报告
            attack_process = await asyncio.create_subprocess_exec(
                *attack_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            report_process = await asyncio.create_subprocess_exec(
                self.vegeta_path, "report", "-type", "json",
                stdin=attack_process.stdout,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 等待完成
            attack_stdout, attack_stderr = await attack_process.communicate()
            report_stdout, report_stderr = await report_process.communicate()
            
            if report_process.returncode != 0:
                raise RuntimeError(f"Vegeta执行失败: {report_stderr.decode()}")
            
            # 解析结果
            result_data = json.loads(report_stdout.decode())
            return self._parse_result(result_data)
            
        finally:
            # 清理临时文件
            os.unlink(target_file)
    
    def _build_target(self, url: str, method: str, headers: Dict[str, str], body: str) -> str:
        """构建Vegeta目标格式"""
        target_lines = [f"{method} {url}"]
        
        if headers:
            for key, value in headers.items():
                target_lines.append(f"{key}: {value}")
        
        if body:
            # 创建临时body文件
            with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.json') as f:
                f.write(body)
                target_lines.append(f"@{f.name}")
        
        return "\n".join(target_lines) + "\n"
    
    def _parse_result(self, data: Dict[str, Any]) -> VegetaResult:
        """解析Vegeta结果"""
        return VegetaResult(
            latencies={
                "mean": data["latencies"]["mean"] / 1e6,  # 转换为毫秒
                "p50": data["latencies"]["50th"] / 1e6,
                "p95": data["latencies"]["95th"] / 1e6,
                "p99": data["latencies"]["99th"] / 1e6,
                "max": data["latencies"]["max"] / 1e6,
                "min": data["latencies"]["min"] / 1e6,
            },
            bytes_in=data["bytes_in"],
            bytes_out=data["bytes_out"],
            duration=data["duration"] / 1e9,  # 转换为秒
            requests=data["requests"],
            rate=data["rate"],
            throughput=data["throughput"],
            success=data["success"],
            status_codes=data["status_codes"],
            errors=data["errors"]
        )


async def parallel_vegeta_test():
    """并行Vegeta测试示例"""
    vegeta = VegetaIntegration()
    
    # 测试配置
    test_configs = [
        ("banner-app1", "http://172.31.236.166:8089/engine/bid/transsion", 50, 10),
        ("banner-app2", "http://172.31.236.166:8089/engine/bid/transsion", 20, 10),
        ("native-app1", "http://172.31.236.166:8089/engine/bid/transsion", 100, 10),
        ("native-app2", "http://172.31.236.166:8089/engine/bid/transsion", 30, 10),
    ]
    
    print("开始并行Vegeta测试...")
    start_time = asyncio.get_event_loop().time()
    
    # 并行执行
    tasks = []
    for name, url, qps, duration in test_configs:
        task = asyncio.create_task(
            vegeta.run_qps_test(url=url, qps=qps, duration=duration)
        )
        tasks.append((name, task))
    
    # 收集结果
    results = []
    for name, task in tasks:
        try:
            result = await task
            results.append((name, result))
            print(f"{name}: QPS={result.rate:.2f}, 成功率={result.success:.2%}")
        except Exception as e:
            print(f"{name} 测试失败: {e}")
    
    end_time = asyncio.get_event_loop().time()
    
    # 计算总体统计
    total_requests = sum(result.requests for _, result in results)
    total_duration = end_time - start_time
    actual_total_qps = total_requests / total_duration
    
    print(f"\n=== 总体统计 ===")
    print(f"总耗时: {total_duration:.2f}秒")
    print(f"总请求数: {total_requests}")
    print(f"实际总QPS: {actual_total_qps:.2f}")


if __name__ == "__main__":
    asyncio.run(parallel_vegeta_test())
