import requests
import json
import time
import threading
from datetime import datetime
from collections import defaultdict

import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation

# 恢复默认样式
# plt.style.use('seaborn-v0_8-darkgrid')

# --- 配置区 ---
BASE_URL = "http://172.31.236.166:8089/dataTest" # 请替换为你的服务地址
SSP_QPS_URL = f"{BASE_URL}/sspQps"
DSP_QPS_URL = f"{BASE_URL}/dspQps"
DSP_BID_URL = f"{BASE_URL}/dspBid"
CTRL_QPS_URL = f"{BASE_URL}/ctrlQps"

SAMPLE_INTERVAL = 3
TIME_WINDOW_SIZE = 80 # 减少点数，以便看清数值，100个点是最近200秒

# --- 全局数据存储 ---
ssp_qps_data = defaultdict(lambda: ([], []))
dsp_qps_data = defaultdict(lambda: ([], []))
dsp_bid_data = defaultdict(lambda: ([], []))
ctrl_qps_data = defaultdict(lambda: ([], []))

data_lock = threading.Lock()
timeout = 5
# --- 数据采集函数 (与之前版本相同) ---
def fetch_data():
    while True:
        try:
            ssp_res = requests.get(SSP_QPS_URL, timeout=timeout).json()
            dsp_res = requests.get(DSP_QPS_URL, timeout=timeout).json()
            bid_res = requests.get(DSP_BID_URL, timeout=timeout).json()
            ctrl_res = requests.get(CTRL_QPS_URL, timeout=timeout).json()
            
            current_time = datetime.now()

            with data_lock:
                update_data_series(ssp_qps_data, ssp_res, current_time)
                update_data_series(dsp_qps_data, dsp_res, current_time)
                update_data_series(dsp_bid_data, bid_res, current_time)
                update_data_series(ctrl_qps_data, ctrl_res, current_time)

        except requests.exceptions.RequestException as e:
            print(f"[{datetime.now()}] Error fetching data: {e}")
        except json.JSONDecodeError as e:
            print(f"[{datetime.now()}] Error decoding JSON: {e}")
            
        time.sleep(SAMPLE_INTERVAL)

def update_data_series(data_dict, new_data, timestamp):
    for key, value in new_data.items():
        timestamps, values = data_dict[key]
        timestamps.append(timestamp)
        values.append(value)
        
        if len(timestamps) > TIME_WINDOW_SIZE:
            timestamps.pop(0)
            values.pop(0)
            
# --- 绘图函数 (有改动) ---
fig, axs = plt.subplots(4, 1, figsize=(15, 18), sharex=True)
fig.suptitle('Real-time QPS Monitor with Values', fontsize=16)

# 用于存储线条和文本标注对象
lines = { 'ssp': {}, 'dsp': {}, 'bid': {}, 'ctrl': {} }
annotations = { 'ssp': {}, 'dsp': {}, 'bid': {}, 'ctrl': {} }
# ma_lines = { 'ssp': {}, 'dsp': {}, 'bid': {}, 'ctrl': {} } # 移除移动平均线


def setup_plot():
    """设置4个子图的标题、标签等 (Y轴标签已更新)"""
    titles = [
        'Supply-Side QPS (sspQps)', 
        'DSP Request QPS (dspQps)', 
        'DSP Bidding QPS (dspBid)', 
        'Final Control QPS (ctrlQps)'
    ]
    y_labels = [
        'QPS (Available Supply)',
        'QPS (Actual Consumption)',
        'QPS (Effective Bids)',
        'QPS (Adjusted Limit)'
    ]
    for i, ax in enumerate(axs):
        ax.set_title(titles[i])
        ax.set_ylabel(y_labels[i]) # 使用更详细的Y轴标签
        ax.grid(True)
    axs[-1].set_xlabel('Time')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])


def update_plot(frame):
    """FuncAnimation的回调函数，用于更新图表"""
    with data_lock:
        update_subplot(axs[0], ssp_qps_data, lines['ssp'], annotations['ssp'])
        update_subplot(axs[1], dsp_qps_data, lines['dsp'], annotations['dsp'])
        update_subplot(axs[2], dsp_bid_data, lines['bid'], annotations['bid'])
        update_subplot(axs[3], ctrl_qps_data, lines['ctrl'], annotations['ctrl'])

    for ax in axs:
        ax.relim()
        ax.autoscale_view()
        
    for ax in axs:
        if ax.get_legend() is None and any(ax.lines):
             ax.legend(loc='upper left', fontsize='small')
        elif any(ax.lines):
            handles, labels = ax.get_legend_handles_labels()
            # 避免图例重复
            unique_labels = dict(zip(labels, handles))
            ax.legend(unique_labels.values(), unique_labels.keys(), loc='upper left', fontsize='small')

def update_subplot(ax, data_dict, line_dict, annotation_dict):
    """更新单个子图，并只在转折点添加/更新数值标注"""
    # 先移除旧的文本标注，避免重叠
    for key in list(annotation_dict.keys()):
        if key in annotation_dict and annotation_dict[key]:
            for ann in annotation_dict[key]:
                ann.remove()
        annotation_dict[key] = []

    for key, (timestamps, values) in data_dict.items():
        if not timestamps:
            continue
            
        if key not in line_dict:
            line, = ax.plot(timestamps, values, marker='o', linestyle='-', label=key, markersize=4)
            line_dict[key] = line
            annotation_dict[key] = []
        else:
            line = line_dict[key]
            line.set_data(timestamps, values)

        # 移除移动平均线逻辑

        # 只在转折点添加文本标注
        for i, (x, y) in enumerate(zip(timestamps, values)):
            # 转折点判断：当前点的值与上一个点不同
            if i == 0 or (i > 0 and y != values[i-1]):
                # 格式化数值，如果是整数则不显示小数点
                label = f"{y:.1f}" if y % 1 != 0 else f"{int(y)}"
                ann = ax.annotate(label, (x, y), textcoords="offset points", xytext=(0, 5), ha='center', fontsize=8)
                annotation_dict[key].append(ann)


if __name__ == '__main__':
    setup_plot()

    data_fetch_thread = threading.Thread(target=fetch_data, daemon=True)
    data_fetch_thread.start()

    ani = FuncAnimation(fig, update_plot, interval=SAMPLE_INTERVAL * 1000, cache_frame_data=False)
    
    plt.show()