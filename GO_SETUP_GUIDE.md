# Go原生QPS测试工具完整配置指南

## 🚀 快速开始

### 1. 一键安装和运行
```bash
# 安装Go环境和依赖
make install

# 构建并运行
make run
```

### 2. 手动安装步骤

#### 步骤1: 安装Go
```bash
# macOS
brew install go

# 验证安装
go version
```

#### 步骤2: 设置环境变量
```bash
# 添加到 ~/.zshrc 或 ~/.bash_profile
echo 'export GOPATH=$HOME/go' >> ~/.zshrc
echo 'export PATH=$PATH:$GOPATH/bin' >> ~/.zshrc
source ~/.zshrc
```

#### 步骤3: 构建项目
```bash
# 方式1: 使用Makefile
make build

# 方式2: 使用脚本
chmod +x build.sh
./build.sh

# 方式3: 手动构建
go mod tidy
go build -o qps-tester main.go
```

#### 步骤4: 运行测试
```bash
# 方式1: 使用Makefile
make run

# 方式2: 使用脚本
chmod +x run.sh
./run.sh

# 方式3: 直接运行
./qps-tester
```

## 📁 项目结构

```
qps-test/
├── main.go                    # Go主程序
├── go.mod                     # Go模块文件
├── go.sum                     # 依赖锁定文件
├── Makefile                   # 构建管理
├── build.sh                   # 构建脚本
├── run.sh                     # 运行脚本
├── banner-app1.json           # Banner测试数据1
├── banner-app2.json           # Banner测试数据2
├── native-app1.json           # Native测试数据1
├── native-app2.json           # Native测试数据2
└── qps-tester                 # 编译后的可执行文件
```

## ⚙️ 配置说明

### 测试配置
在 `main.go` 中修改测试配置：

```go
configs := []TestConfig{
    {
        Name:     "banner-app1",
        URL:      "http://172.31.236.166:8089/engine/bid/transsion",
        JSONFile: "banner-app1.json",
        QPS:      50,
        Duration: 10,  // 秒
        Headers:  map[string]string{"Content-Type": "application/json"},
    },
    // 更多配置...
}
```

### 参数说明
- `Name`: 测试名称
- `URL`: 目标服务器地址
- `JSONFile`: JSON请求体文件路径
- `QPS`: 目标每秒请求数
- `Duration`: 测试持续时间（秒）
- `Headers`: HTTP请求头

## 🎯 运行示例

### 预期输出
```
🚀 开始Go原生并行QPS测试
目标总QPS: 240

[banner-app1] 发送请求 100% |████████████████| (100/100, 50 req/s)
[banner-app2] 发送请求 100% |████████████████| (40/40, 20 req/s)
[native-app1] 发送请求 100% |████████████████| (240/240, 120 req/s)
[native-app2] 发送请求 100% |████████████████| (100/100, 50 req/s)

=== Go原生测试完成统计 ===
banner-app1: 实际QPS=49.85, 请求数=100, 成功率=99.00%, P95延迟=45ms
banner-app2: 实际QPS=19.92, 请求数=40, 成功率=97.50%, P95延迟=52ms
native-app1: 实际QPS=119.76, 请求数=240, 成功率=99.58%, P95延迟=38ms
native-app2: 实际QPS=49.88, 请求数=100, 成功率=98.00%, P95延迟=41ms

总耗时: 2.05s
总请求数: 480
实际总QPS: 238.41
总成功率: 98.75%
```

## 🔧 高级功能

### 1. 性能分析
```bash
# 启用性能分析
go build -o qps-tester main.go
./qps-tester -cpuprofile=cpu.prof -memprofile=mem.prof

# 查看性能报告
go tool pprof cpu.prof
go tool pprof mem.prof
```

### 2. 交叉编译
```bash
# Linux版本
make build-linux

# Windows版本  
make build-windows

# 所有平台
make build-all
```

### 3. 代码检查
```bash
# 格式化代码
make fmt

# 代码检查
make lint
```

## 📊 性能对比

| 指标 | Python原生 | Go原生 | 提升倍数 |
|------|------------|--------|----------|
| 最大QPS | ~5,000 | 100,000+ | **20x+** |
| 内存使用 | 高 | 极低 | **5-10x** |
| CPU使用 | 中等 | 极低 | **3-5x** |
| 启动时间 | 慢 | 极快 | **10x+** |
| 二进制大小 | N/A | ~15MB | 单文件 |

## 🛠 故障排除

### 1. Go未安装
```
错误: command not found: go
解决: brew install go
```

### 2. 依赖下载失败
```bash
# 设置Go代理
export GOPROXY=https://goproxy.cn,direct
go mod tidy
```

### 3. JSON文件不存在
```
错误: 加载JSON文件失败
解决: 确保JSON文件存在且格式正确
```

### 4. 端口被占用
```bash
# 检查端口占用
lsof -i :8080

# 修改配置中的端口
```

## 🚀 性能优化建议

### 1. 系统优化
```bash
# 增加文件描述符限制
ulimit -n 65536

# 增加进程限制
ulimit -u 32768
```

### 2. 网络优化
```bash
# 调整TCP参数
sudo sysctl -w net.core.somaxconn=65535
sudo sysctl -w net.ipv4.tcp_max_syn_backlog=65535
```

### 3. Go程序优化
```go
// 在main.go中调整
MaxIdleConns:        10000,  // 增加连接池
MaxIdleConnsPerHost: 1000,   // 增加每主机连接数
IdleConnTimeout:     30 * time.Second,
```

## 📈 扩展功能

### 1. 添加新的测试类型
```go
// 在TestConfig中添加新字段
type TestConfig struct {
    // ... 现有字段
    Method   string `json:"method"`   // 支持GET/POST/PUT等
    Timeout  int    `json:"timeout"`  // 自定义超时
}
```

### 2. 结果导出
```go
// 添加结果导出功能
func exportResults(results []*TestResult, filename string) error {
    data, _ := json.MarshalIndent(results, "", "  ")
    return os.WriteFile(filename, data, 0644)
}
```

### 3. 实时监控
```go
// 添加实时监控
func startMonitoring() {
    go func() {
        for {
            // 输出实时统计
            time.Sleep(time.Second)
        }
    }()
}
```

## 🎯 最佳实践

1. **逐步增加QPS**: 从低QPS开始测试
2. **监控系统资源**: 关注CPU、内存、网络使用率
3. **合理设置超时**: 避免请求堆积
4. **定期清理**: 清理日志和临时文件
5. **版本控制**: 使用Git管理配置变更

## 📞 技术支持

如遇问题，请检查：
1. Go版本是否 >= 1.19
2. JSON文件格式是否正确
3. 网络连接是否正常
4. 系统资源是否充足
5. 防火墙设置是否正确
