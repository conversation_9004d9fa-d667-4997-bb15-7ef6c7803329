# from qps_request import test_banner, test_native

# # banner测试app1 0.4
# result = test_banner(
#     qps=50,
#     duration=10,
#     width=690,
#     height=1035,
#     app_bundle="com.transsion.XOSLauncher",
#     device_ip="***************"
# )
# # Banner测试_app2 0.1
# result = test_banner(
#     qps=20,
#     duration=10,
#     width=690,
#     height=1035,
#     app_bundle="com.transsion.XOSLauncher2",
#     device_ip="***************"
# )

# # Native测试 app1 0.4
# result = test_native(
#     qps=50,
#     duration=10,
#     width=451,
#     height=270,
#     app_bundle="com.transsion.XOSLauncher",
#     device_ip="************"
# )  
# # Native 测试app2 0.1
# result = test_native(
#     qps=20,
#     duration=10,
#     width=451,
#     height=270,
#     app_bundle="com.transsion.XOSLauncher2",
#     device_ip="************"
# )

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
传音QPS测试示例
"""

import asyncio
import json
import uuid
from qps_tester import QpsTester
from config.request_config import BaseRequestBuilder, MediaInfo, AdRequestInfo
from core.qps_controller import LeakyBucketController


class TranssionRequestBuilder(BaseRequestBuilder):
    """传音请求构建器"""

    def __init__(self):
        media_info = MediaInfo(
            name="transsion",
            endpoint_url="http://**************:8089/engine/bid/transsion",
            timeout=10.0
        )
        super().__init__(media_info)
        self.ad_type = "transsion"

    def build_payload(self, ad_request: AdRequestInfo) -> dict:
        """构建传音请求payload"""

        # 根据广告类型加载不同模板
        if ad_request.ad_type == "banner":
            return self._build_banner_payload(ad_request)
        elif ad_request.ad_type == "native":
            return self._build_native_payload(ad_request)
        else:
            raise ValueError(f"不支持的广告类型: {ad_request.ad_type}")

    def _build_banner_payload(self, ad_request: AdRequestInfo) -> dict:
        """构建Banner请求"""
        # 加载Banner模板
        with open('transsion-banner-app-country.json', 'r', encoding='utf-8') as f:
            payload = json.load(f)

        # 设置基本参数
        payload['id'] = str(uuid.uuid4())
        payload['imp'][0]['banner']['w'] = ad_request.width or 690
        payload['imp'][0]['banner']['h'] = ad_request.height or 1035
        payload['app']['bundle'] = ad_request.app_bundle or "com.transsion.XOSLauncher"
        payload['device']['ip'] = ad_request.device_ip or "***************"

        # 应用自定义参数
        self._apply_custom_params(payload, ad_request.custom_params)

        return payload

    def _build_native_payload(self, ad_request: AdRequestInfo) -> dict:
        """构建Native请求"""
        # 加载Native模板
        with open('transsion-native-app-country.json', 'r', encoding='utf-8') as f:
            payload = json.load(f)

        # 设置基本参数
        payload['id'] = str(uuid.uuid4())
        payload['app']['bundle'] = ad_request.app_bundle or "com.transsion.XOSLauncher"
        payload['device']['ip'] = ad_request.device_ip or "************"

        # 修改native request中的图片尺寸
        if ad_request.width and ad_request.height:
            self._update_native_image_size(payload, ad_request.width, ad_request.height)

        # 应用自定义参数
        self._apply_custom_params(payload, ad_request.custom_params)

        return payload

    def _update_native_image_size(self, payload: dict, width: int, height: int):
        """更新Native图片尺寸"""
        try:
            request_str = payload['imp'][0]['native']['request']
            request_obj = json.loads(request_str)

            # 查找type=3的图片资源并更新尺寸
            for asset in request_obj['native']['assets']:
                if 'img' in asset and asset['img'].get('type') == 3:
                    asset['img']['wmin'] = width
                    asset['img']['hmin'] = height
                    break

            payload['imp'][0]['native']['request'] = json.dumps(request_obj)
        except (KeyError, json.JSONDecodeError) as e:
            print(f"更新Native图片尺寸失败: {e}")

    def _apply_custom_params(self, payload: dict, custom_params: dict):
        """应用自定义参数"""
        for key, value in custom_params.items():
            if '.' in key:
                # 支持嵌套参数，如 "app.name"
                keys = key.split('.')
                current = payload
                for k in keys[:-1]:
                    if k not in current:
                        current[k] = {}
                    current = current[k]
                current[keys[-1]] = value
            else:
                payload[key] = value


async def qps_test(
    tester: QpsTester,
    request_builder: TranssionRequestBuilder,
    ad_request: AdRequestInfo,
    qps: int,
    duration: int
):
    result = await tester.run_qps_test(
        request_builder=request_builder,
        ad_request=ad_request,
        qps=qps,
        duration=duration
    )
    return result


# Vegeta集成版本
async def vegeta_qps_test(json_file: str, url: str, qps: int, duration: int, headers: dict = None):
    """使用Vegeta进行QPS测试的简化版本"""
    from vegeta_integration import VegetaIntegration, SimpleTestConfig

    vegeta = VegetaIntegration()
    config = SimpleTestConfig(
        name="test",
        url=url,
        json_file=json_file,
        qps=qps,
        duration=duration,
        headers=headers or {"Content-Type": "application/json"}
    )

    return await vegeta.run_simple_test(config)


async def main():
    request_builder = TranssionRequestBuilder()

    # 测试配置：(名称, 请求配置, QPS, 持续时间)
    test_configs = [
        # ("banner-app1", AdRequestInfo(
        #     ad_type="banner", width=690, height=1035,
        #     app_bundle="app1", device_ip="***************"
        # ),50, 1000),
        # ("banner-app2", AdRequestInfo(
        #     ad_type="banner", width=690, height=1035,
        #     app_bundle="app2", device_ip="***************"
        # ), 10, 1000),
        # ("native-app", AdRequestInfo(
        #     ad_type="native", width=690, height=1035,
        #     app_bundle="app1", device_ip="***************"
        # ), 50, 1000),
        # ("native-app2", AdRequestInfo(
        #     ad_type="native", width=690, height=1035,
        #     app_bundle="app2", device_ip="***************"
        # ), 10, 1000),
        ("native-app3", AdRequestInfo(
            ad_type="native", width=451, height=270,
            app_bundle="app3", device_ip="************"
        ), 100, 1000),
        # ("native-app2", AdRequestInfo(
        #     ad_type="native", width=451, height=270,
        #     app_bundle="com.transsion.XOSLauncher2", device_ip="************"
        # ), 15, 3)
    ]

    # 计算配置的总QPS
    target_total_qps = sum(config[2] for config in test_configs)
    print(f"开始并行执行QPS测试，目标总QPS: {target_total_qps}")

    # 创建一个共享的QPS控制器
    # 我们将所有QPS配置聚合到一起，以实现更平滑的流量控制
    # LeakyBucketController 非常适合这种场景
    qps_controller = LeakyBucketController(qps=target_total_qps)

    start_time = asyncio.get_event_loop().time()

    async with QpsTester(qps_controller) as tester:
        # 并行执行所有测试并收集结果
        tasks = [
            qps_test(tester, request_builder, config[1], config[2], config[3])
            for config in test_configs
        ]
        results = await asyncio.gather(*tasks)

    end_time = asyncio.get_event_loop().time()
    total_duration = end_time - start_time

    # 计算真实QPS统计
    total_requests = sum(result.total_requests for result in results)
    total_success_requests = sum(result.success_requests for result in results)
    actual_total_qps = total_requests / total_duration if total_duration > 0 else 0

    print(f"\n=== 测试完成统计 ===")
    print(f"总耗时: {total_duration:.2f}秒")
    print(f"目标总QPS: {target_total_qps}")
    print(f"实际总QPS: {actual_total_qps:.2f}")
    print(f"总请求数: {total_requests}")
    print(f"成功请求数: {total_success_requests}")
    print(f"总成功率: {(total_success_requests/total_requests*100):.2f}%" if total_requests > 0 else "0%")

    print(f"\n=== 各测试详情 ===")
    for config, result in zip(test_configs, results):
        test_name, _, target_qps, _ = config
        print(f"{test_name}: 目标QPS={target_qps}, 实际QPS={result.actual_qps}, 请求数={result.total_requests}")





async def vegeta_main():
    """使用Vegeta的简化版本"""
    # 简化的测试配置 - 直接使用JSON文件
    test_configs = [
        {
            "name": "banner-app1",
            "url": "http://**************:8089/engine/bid/transsion",
            "json_file": "banner-app1.json",
            "qps": 50,
            "duration": 2,
            "headers": {"Content-Type": "application/json"}

        },
        {
            "name": "banner-app2",
            "url": "http://**************:8089/engine/bid/transsion",
            "json_file": "banner-app2.json",
            "qps": 20,
            "duration": 2,
            "headers": {"Content-Type": "application/json"}
        },
        {
            "name": "native-app1",
            "url": "http://**************:8089/engine/bid/transsion",
            "json_file": "native-app1.json",
            "qps": 120,
            "duration": 2,
            "headers": {"Content-Type": "application/json"}
        },
        {
            "name": "native-app2",
            "url": "http://**************:8089/engine/bid/transsion",
            "json_file": "native-app2.json",
            "qps": 50,
            "duration": 2,
            "headers": {"Content-Type": "application/json"}
        }
    ]

    # 计算配置的总QPS
    target_total_qps = sum(config["qps"] for config in test_configs)
    print(f"开始Vegeta并行执行QPS测试，目标总QPS: {target_total_qps}")

    start_time = asyncio.get_event_loop().time()

    # 并行执行所有测试并收集结果
    results = await asyncio.gather(*[
        vegeta_qps_test(
            json_file=config["json_file"],
            url=config["url"],
            qps=config["qps"],
            duration=config["duration"],
            headers=config.get("headers")
        )
        for config in test_configs
    ])

    end_time = asyncio.get_event_loop().time()
    total_duration = end_time - start_time

    # 计算真实QPS统计
    total_requests = sum(result.requests for result in results)
    total_success_requests = sum(int(result.requests * result.success) for result in results)
    actual_total_qps = total_requests / total_duration if total_duration > 0 else 0

    print(f"\n=== Vegeta测试完成统计 ===")
    print(f"总耗时: {total_duration:.2f}秒")
    print(f"目标总QPS: {target_total_qps}")
    print(f"实际总QPS: {actual_total_qps:.2f}")
    print(f"总请求数: {total_requests}")
    print(f"成功请求数: {total_success_requests}")
    print(f"总成功率: {(total_success_requests/total_requests*100):.2f}%" if total_requests > 0 else "0%")

    print(f"\n=== 各测试详情 ===")
    for config, result in zip(test_configs, results):
        print(f"{config['name']}: 目标QPS={config['qps']}, 实际QPS={result.rate:.2f}, "
              f"请求数={result.requests}, 成功率={result.success:.2%}, "
              f"P95延迟={result.latencies['p95']:.2f}ms")


if __name__ == "__main__":
    # 选择运行方式
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "vegeta":
        print("使用Vegeta高性能模式")
        asyncio.run(vegeta_main())
    else:
        print("使用Python原生模式")
        asyncio.run(main())
