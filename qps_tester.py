#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
通用QPS测试器 - 主要接口
这是您需要使用的主要接口，只需要传入媒体信息和请求信息即可
"""

import asyncio
from typing import List, Dict, Any, Optional

from core.http_client import AsyncHttpClient
from core.qps_controller import AsyncQpsController, QpsController
from core.executor import RequestExecutor, ExecutionResult
from core.analyzer import ResultAnalyzer
from config.request_config import BaseRequestBuilder, MediaInfo, AdRequestInfo


class QpsTester:
    """通用QPS测试器 - 这是您的主要接口"""
    
    def __init__(self, qps_controller: Optional[QpsController] = None):
        self.http_client: Optional[AsyncHttpClient] = None
        self.analyzer = ResultAnalyzer()
        self.qps_controller = qps_controller
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.http_client = AsyncHttpClient()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.http_client:
            await self.http_client.close()
    
    async def run_qps_test(self,
                          request_builder: BaseRequestBuilder,
                          ad_request: AdRequestInfo,
                          qps: int,
                          duration: int,
                          max_concurrent: Optional[int] = None) -> ExecutionResult:
        """
        运行QPS测试 - 核心接口
        
        Args:
            request_builder: 请求构建器（您需要实现的）
            ad_request: 广告请求信息
            qps: 目标QPS
            duration: 持续时间（秒）
            max_concurrent: 最大并发数
            
        Returns:
            ExecutionResult: 测试结果
        """
        if not self.http_client:
            raise RuntimeError("HTTP client not initialized. Use async context manager.")
        
        # 如果没有提供外部QPS控制器，则创建一个
        qps_controller = self.qps_controller or AsyncQpsController(qps, max_concurrent)
        
        # 创建执行器
        executor = RequestExecutor(self.http_client, qps_controller)
        
        # 执行测试
        result =  await executor.execute_test(request_builder, ad_request, qps, duration)
        print(f"   实际QPS: {result.actual_qps}")
        print(f"   成功率: {result.success_rate:.2f}%")
        print(f"   填充率: {result.fill_rate:.2f}%")
        print(f"   总请求数: {result.total_requests}")
        return result
    
    async def run_qps_range_test(self,
                                request_builder: BaseRequestBuilder,
                                ad_request: AdRequestInfo,
                                qps_list: List[int],
                                duration: int,
                                interval: int = 2) -> List[ExecutionResult]:
        """
        运行QPS范围测试
        
        Args:
            request_builder: 请求构建器
            ad_request: 广告请求信息
            qps_list: QPS列表
            duration: 每个测试的持续时间
            interval: 测试间隔时间
            
        Returns:
            List[ExecutionResult]: 测试结果列表
        """
        results = []
        
        for qps in qps_list:
            print(f"\n--- 开始QPS {qps} 测试 ---")
            result = await self.run_qps_test(request_builder, ad_request, qps, duration)
            results.append(result)
            
            # 测试间隔
            if interval > 0:
                print(f"等待 {interval} 秒后开始下一个测试...")
                await asyncio.sleep(interval)
        
        return results
    
    async def run_stress_test(self,
                             request_builder: BaseRequestBuilder,
                             ad_request: AdRequestInfo,
                             start_qps: int = 10,
                             max_qps: int = 100,
                             step: int = 10,
                             duration: int = 30,
                             success_rate_threshold: float = 95.0) -> List[ExecutionResult]:
        """
        运行压力测试
        
        Args:
            request_builder: 请求构建器
            ad_request: 广告请求信息
            start_qps: 起始QPS
            max_qps: 最大QPS
            step: QPS步长
            duration: 每个测试的持续时间
            success_rate_threshold: 成功率阈值
            
        Returns:
            List[ExecutionResult]: 测试结果列表
        """
        results = []
        current_qps = start_qps
        
        print(f"开始压力测试: {start_qps} -> {max_qps} QPS (步长: {step})")
        print(f"成功率阈值: {success_rate_threshold}%")
        
        while current_qps <= max_qps:
            print(f"\n--- 压力测试 QPS: {current_qps} ---")
            
            result = await self.run_qps_test(request_builder, ad_request, current_qps, duration)
            results.append(result)
            
            # 检查成功率
            if result.success_rate < success_rate_threshold:
                print(f"⚠️  成功率 {result.success_rate:.2f}% 低于阈值 {success_rate_threshold}%")
                print(f"最大可承受QPS: {current_qps - step}")
                break
            
            current_qps += step
            
            # 短暂休息
            await asyncio.sleep(1)
        
        return results
    
    def analyze_results(self, results: List[ExecutionResult]) -> Dict[str, Any]:
        """分析测试结果"""
        if len(results) == 1:
            return self.analyzer.analyze_single_result(results[0])
        else:
            return self.analyzer.compare_results(results)
    
    def export_results(self, results: List[ExecutionResult], 
                      filename: Optional[str] = None) -> str:
        """导出结果"""
        return self.analyzer.export_results(results, filename)


# 便捷函数 - 简化使用
async def run_single_qps_test(request_builder: BaseRequestBuilder,
                             ad_request: AdRequestInfo,
                             qps: int,
                             duration: int,
                             **kwargs) -> ExecutionResult:
    """运行单个QPS测试的便捷函数"""
    async with QpsTester() as tester:
        return await tester.run_qps_test(request_builder, ad_request, qps, duration, **kwargs)


async def run_qps_range_test(request_builder: BaseRequestBuilder,
                            ad_request: AdRequestInfo,
                            qps_list: List[int],
                            duration: int,
                            **kwargs) -> List[ExecutionResult]:
    """运行QPS范围测试的便捷函数"""
    async with QpsTester() as tester:
        return await tester.run_qps_range_test(request_builder, ad_request, qps_list, duration, **kwargs)


async def run_stress_test(request_builder: BaseRequestBuilder,
                         ad_request: AdRequestInfo,
                         **kwargs) -> List[ExecutionResult]:
    """运行压力测试的便捷函数"""
    async with QpsTester() as tester:
        return await tester.run_stress_test(request_builder, ad_request, **kwargs)


# 示例：如何使用这个接口
if __name__ == "__main__":
    # 这里展示如何使用通用接口
    from config.request_config import TranssionBannerBuilder
    
    async def example():
        # 1. 创建请求构建器（您需要实现自己的）
        request_builder = TranssionBannerBuilder()
        
        # 2. 创建广告请求信息
        ad_request = AdRequestInfo(
            ad_type="banner",
            width=690,
            height=1035,
            app_bundle="com.transsion.XOSLauncher2",
            device_ip="***************"
        )
        
        # 3. 运行测试
        async with QpsTester() as tester:
            # 单个QPS测试
            result = await tester.run_qps_test(request_builder, ad_request, qps=20, duration=10)
            print(f"测试完成 - 实际QPS: {result.actual_qps}, 成功率: {result.success_rate:.2f}%")
            
            # QPS范围测试
            results = await tester.run_qps_range_test(
                request_builder, ad_request, 
                qps_list=[5, 10, 15, 20], 
                duration=5
            )
            
            # 分析结果
            analysis = tester.analyze_results(results)
            print("分析完成")
            
            # 导出结果
            filename = tester.export_results(results)
            print(f"结果已导出到: {filename}")
    
    asyncio.run(example())
