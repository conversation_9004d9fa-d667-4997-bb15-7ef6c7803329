use std::collections::HashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Semaphore;
use tokio::time::{interval, MissedTickBehavior};
use reqwest::Client;
use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct RequestConfig {
    pub method: String,
    pub url: String,
    pub headers: HashMap<String, String>,
    pub body: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct TestResult {
    pub total_requests: u64,
    pub success_requests: u64,
    pub failed_requests: u64,
    pub total_duration: Duration,
    pub actual_qps: f64,
    pub avg_latency: Duration,
    pub p95_latency: Duration,
    pub p99_latency: Duration,
    pub status_codes: HashMap<u16, u64>,
    pub errors: Vec<String>,
}

#[derive(Debug)]
pub struct RequestResult {
    pub status_code: Option<u16>,
    pub latency: Duration,
    pub error: Option<String>,
    pub timestamp: Instant,
}

/// 高精度QPS控制器
pub struct QpsController {
    rate: f64,
    semaphore: Arc<Semaphore>,
    token_count: Arc<AtomicU64>,
}

impl QpsController {
    pub fn new(qps: f64) -> Self {
        let permits = (qps * 2.0) as usize; // 缓冲区大小
        let semaphore = Arc::new(Semaphore::new(permits));
        let token_count = Arc::new(AtomicU64::new(0));
        
        // 启动令牌生成器
        let sem_clone = semaphore.clone();
        let count_clone = token_count.clone();
        let interval_duration = Duration::from_nanos((1_000_000_000.0 / qps) as u64);
        
        tokio::spawn(async move {
            let mut interval = interval(interval_duration);
            interval.set_missed_tick_behavior(MissedTickBehavior::Skip);
            
            loop {
                interval.tick().await;
                
                // 尝试添加令牌（非阻塞）
                if let Ok(_permit) = sem_clone.try_acquire() {
                    count_clone.fetch_add(1, Ordering::Relaxed);
                    // 立即释放permit，让acquire()可以获取
                    drop(_permit);
                }
            }
        });
        
        Self {
            rate: qps,
            semaphore,
            token_count,
        }
    }
    
    pub async fn acquire(&self) {
        let _permit = self.semaphore.acquire().await.unwrap();
        self.token_count.fetch_sub(1, Ordering::Relaxed);
        // permit在这里自动释放
    }
}

/// 高性能QPS测试器
pub struct QpsTester {
    client: Client,
    controller: QpsController,
}

impl QpsTester {
    pub fn new(qps: f64, timeout: Duration) -> Self {
        let client = Client::builder()
            .timeout(timeout)
            .pool_max_idle_per_host(100)
            .pool_idle_timeout(Duration::from_secs(90))
            .build()
            .unwrap();
        
        Self {
            client,
            controller: QpsController::new(qps),
        }
    }
    
    pub async fn run_test(
        &self,
        config: RequestConfig,
        duration: Duration,
    ) -> Result<TestResult, Box<dyn std::error::Error>> {
        let start_time = Instant::now();
        let end_time = start_time + duration;
        
        let mut tasks = Vec::new();
        let request_count = Arc::new(AtomicU64::new(0));
        
        // 启动请求发送任务
        while Instant::now() < end_time {
            self.controller.acquire().await;
            
            let client = self.client.clone();
            let config = config.clone();
            let count = request_count.clone();
            
            let task = tokio::spawn(async move {
                let result = Self::send_request(client, config).await;
                count.fetch_add(1, Ordering::Relaxed);
                result
            });
            
            tasks.push(task);
        }
        
        // 等待所有请求完成
        let mut results = Vec::new();
        for task in tasks {
            if let Ok(result) = task.await {
                results.push(result);
            }
        }
        
        let total_duration = start_time.elapsed();
        let total_requests = request_count.load(Ordering::Relaxed);
        
        Ok(self.calculate_results(results, total_duration, total_requests))
    }
    
    async fn send_request(
        client: Client,
        config: RequestConfig,
    ) -> RequestResult {
        let start = Instant::now();
        
        let mut request_builder = match config.method.as_str() {
            "GET" => client.get(&config.url),
            "POST" => client.post(&config.url),
            "PUT" => client.put(&config.url),
            "DELETE" => client.delete(&config.url),
            _ => client.get(&config.url),
        };
        
        // 添加请求头
        for (key, value) in config.headers {
            request_builder = request_builder.header(&key, &value);
        }
        
        // 添加请求体
        if let Some(body) = config.body {
            request_builder = request_builder.body(body);
        }
        
        let latency = start.elapsed();
        
        match request_builder.send().await {
            Ok(response) => RequestResult {
                status_code: Some(response.status().as_u16()),
                latency,
                error: None,
                timestamp: start,
            },
            Err(e) => RequestResult {
                status_code: None,
                latency,
                error: Some(e.to_string()),
                timestamp: start,
            },
        }
    }
    
    fn calculate_results(
        &self,
        results: Vec<RequestResult>,
        total_duration: Duration,
        total_requests: u64,
    ) -> TestResult {
        if results.is_empty() {
            return TestResult {
                total_requests: 0,
                success_requests: 0,
                failed_requests: 0,
                total_duration,
                actual_qps: 0.0,
                avg_latency: Duration::from_secs(0),
                p95_latency: Duration::from_secs(0),
                p99_latency: Duration::from_secs(0),
                status_codes: HashMap::new(),
                errors: Vec::new(),
            };
        }
        
        let mut success_count = 0u64;
        let mut failed_count = 0u64;
        let mut total_latency = Duration::from_secs(0);
        let mut status_codes = HashMap::new();
        let mut errors = Vec::new();
        let mut latencies = Vec::new();
        
        for result in &results {
            if let Some(status) = result.status_code {
                success_count += 1;
                *status_codes.entry(status).or_insert(0) += 1;
            } else {
                failed_count += 1;
                if let Some(error) = &result.error {
                    errors.push(error.clone());
                }
            }
            
            total_latency += result.latency;
            latencies.push(result.latency);
        }
        
        // 计算百分位数
        latencies.sort();
        let p95_index = (latencies.len() as f64 * 0.95) as usize;
        let p99_index = (latencies.len() as f64 * 0.99) as usize;
        
        let p95_latency = latencies.get(p95_index.min(latencies.len() - 1))
            .copied()
            .unwrap_or(Duration::from_secs(0));
        let p99_latency = latencies.get(p99_index.min(latencies.len() - 1))
            .copied()
            .unwrap_or(Duration::from_secs(0));
        
        TestResult {
            total_requests,
            success_requests: success_count,
            failed_requests: failed_count,
            total_duration,
            actual_qps: total_requests as f64 / total_duration.as_secs_f64(),
            avg_latency: total_latency / results.len() as u32,
            p95_latency,
            p99_latency,
            status_codes,
            errors,
        }
    }
}

/// 并行测试示例
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let configs = vec![
        (
            "banner-app1",
            RequestConfig {
                method: "POST".to_string(),
                url: "http://172.31.236.166:8089/engine/bid/transsion".to_string(),
                headers: {
                    let mut headers = HashMap::new();
                    headers.insert("Content-Type".to_string(), "application/json".to_string());
                    headers
                },
                body: Some(r#"{"test": "banner-app1"}"#.to_string()),
            },
            50.0,
        ),
        (
            "native-app1",
            RequestConfig {
                method: "POST".to_string(),
                url: "http://172.31.236.166:8089/engine/bid/transsion".to_string(),
                headers: {
                    let mut headers = HashMap::new();
                    headers.insert("Content-Type".to_string(), "application/json".to_string());
                    headers
                },
                body: Some(r#"{"test": "native-app1"}"#.to_string()),
            },
            100.0,
        ),
    ];
    
    let duration = Duration::from_secs(10);
    let timeout = Duration::from_secs(5);
    
    println!("开始并行QPS测试...");
    let start = Instant::now();
    
    let mut tasks = Vec::new();
    
    // 启动并行测试
    for (name, config, qps) in configs {
        let tester = QpsTester::new(qps, timeout);
        let task = tokio::spawn(async move {
            let result = tester.run_test(config, duration).await;
            (name, result)
        });
        tasks.push(task);
    }
    
    // 收集结果
    let mut total_requests = 0u64;
    let mut total_qps = 0.0;
    
    for task in tasks {
        if let Ok((name, result)) = task.await {
            match result {
                Ok(test_result) => {
                    println!(
                        "{}: QPS={:.2}, 成功率={:.2}%, 平均延迟={:?}",
                        name,
                        test_result.actual_qps,
                        test_result.success_requests as f64 / test_result.total_requests as f64 * 100.0,
                        test_result.avg_latency
                    );
                    
                    total_requests += test_result.total_requests;
                    total_qps += test_result.actual_qps;
                }
                Err(e) => {
                    println!("测试 {} 失败: {}", name, e);
                }
            }
        }
    }
    
    let total_duration = start.elapsed();
    println!("\n=== 总体统计 ===");
    println!("总耗时: {:?}", total_duration);
    println!("总请求数: {}", total_requests);
    println!("实际总QPS: {:.2}", total_qps);
    
    Ok(())
}
