# Go原生QPS测试工具 Makefile

.PHONY: help install build run clean test deps

# 默认目标
help:
	@echo "Go原生QPS测试工具"
	@echo "=================="
	@echo ""
	@echo "可用命令:"
	@echo "  make install  - 安装Go环境和依赖"
	@echo "  make deps     - 下载Go依赖"
	@echo "  make build    - 构建项目"
	@echo "  make run      - 运行测试"
	@echo "  make test     - 运行单元测试"
	@echo "  make clean    - 清理构建文件"
	@echo "  make all      - 完整构建和运行"

# 安装Go环境（macOS）
install:
	@echo "🔧 检查Go环境..."
	@if ! command -v go &> /dev/null; then \
		echo "📦 安装Go..."; \
		brew install go; \
	else \
		echo "✅ Go已安装: $$(go version)"; \
	fi
	@echo "📝 设置Go环境变量..."
	@echo 'export GOPATH=$$HOME/go' >> ~/.zshrc || true
	@echo 'export PATH=$$PATH:$$GOPATH/bin' >> ~/.zshrc || true

# 下载依赖
deps:
	@echo "📥 下载Go依赖..."
	go mod tidy
	go mod download

# 构建项目
build: deps
	@echo "🔨 构建项目..."
	go build -ldflags="-s -w" -o qps-tester main.go
	@echo "✅ 构建完成！"

# 运行测试
run: build
	@echo "🚀 运行QPS测试..."
	./qps-tester

# 运行单元测试
test:
	@echo "🧪 运行单元测试..."
	go test -v ./...

# 清理构建文件
clean:
	@echo "🧹 清理构建文件..."
	rm -f qps-tester
	go clean

# 完整流程
all: clean build run

# 检查代码格式
fmt:
	@echo "🎨 格式化代码..."
	go fmt ./...

# 代码检查
lint:
	@echo "🔍 代码检查..."
	@if command -v golangci-lint &> /dev/null; then \
		golangci-lint run; \
	else \
		echo "⚠️  golangci-lint未安装，跳过检查"; \
	fi

# 性能分析
profile: build
	@echo "📊 性能分析..."
	go tool pprof -http=:8080 qps-tester cpu.prof

# 交叉编译
build-linux: deps
	@echo "🐧 构建Linux版本..."
	GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o qps-tester-linux main.go

build-windows: deps
	@echo "🪟 构建Windows版本..."
	GOOS=windows GOARCH=amd64 go build -ldflags="-s -w" -o qps-tester.exe main.go

# 构建所有平台
build-all: build build-linux build-windows
	@echo "✅ 所有平台构建完成！"
