#!/bin/bash

# Go原生QPS测试工具构建脚本

set -e

echo "🔧 检查Go环境..."
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go"
    echo "macOS: brew install go"
    exit 1
fi

echo "✅ Go版本: $(go version)"

echo "📦 初始化Go模块..."
if [ ! -f "go.mod" ]; then
    go mod init qps-tester
fi

echo "📥 下载依赖..."
go mod tidy

echo "🔨 构建项目..."
go build -o qps-tester main.go

echo "✅ 构建完成！"
echo ""
echo "🚀 运行方式:"
echo "   ./qps-tester"
echo ""
echo "📁 确保以下JSON文件存在:"
echo "   - banner-app1.json"
echo "   - banner-app2.json" 
echo "   - native-app1.json"
echo "   - native-app2.json"
