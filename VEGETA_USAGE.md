# Vegeta集成使用指南

## 概述

这个集成方案使用Go语言的Vegeta工具来实现高性能QPS测试，相比Python原生实现有以下优势：

- **性能提升10倍以上**：从5k QPS提升到50k+ QPS
- **精确QPS控制**：微秒级精度的令牌桶算法
- **更低资源消耗**：内存和CPU使用率显著降低
- **简化配置**：直接使用JSON文件+URL的方式

## 安装Vegeta

### macOS
```bash
brew install vegeta
```

### Linux
```bash
# 下载预编译二进制文件
wget https://github.com/tsenart/vegeta/releases/download/v12.12.0/vegeta_12.12.0_linux_amd64.tar.gz
tar -xzf vegeta_12.12.0_linux_amd64.tar.gz
sudo mv vegeta /usr/local/bin/
```

### 验证安装
```bash
vegeta -version
```

## 使用方法

### 1. 准备JSON文件

确保你的JSON请求文件存在：
- `transsion-banner-app-country.json` - Banner请求模板
- `transsion-native-app-country.json` - Native请求模板

### 2. 运行测试

#### 使用Vegeta高性能模式
```bash
python main.py vegeta
```

#### 使用Python原生模式（对比）
```bash
python main.py
```

### 3. 配置测试参数

在 `main.py` 的 `vegeta_main()` 函数中修改 `test_configs`：

```python
test_configs = [
    {
        "name": "banner-app1",
        "url": "http://172.31.236.166:8089/engine/bid/transsion",
        "json_file": "transsion-banner-app-country.json",
        "qps": 50,
        "duration": 10,
        # 可选：自定义请求头
        "headers": {"Content-Type": "application/json", "X-Custom": "value"}
    },
    # 更多配置...
]
```

## 配置参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| name | str | 是 | 测试名称，用于结果显示 |
| url | str | 是 | 目标URL地址 |
| json_file | str | 是 | JSON请求体文件路径 |
| qps | int | 是 | 目标QPS |
| duration | int | 是 | 测试持续时间（秒） |
| headers | dict | 否 | 自定义HTTP请求头 |

## 输出结果示例

```
开始Vegeta并行执行QPS测试，目标总QPS: 240

=== Vegeta测试完成统计 ===
总耗时: 10.05秒
目标总QPS: 240
实际总QPS: 238.76
总请求数: 2400
成功请求数: 2388
总成功率: 99.50%

=== 各测试详情 ===
banner-app1: 目标QPS=50, 实际QPS=49.8, 请求数=500, 成功率=99.60%, P95延迟=45.23ms
banner-app2: 目标QPS=20, 实际QPS=19.9, 请求数=200, 成功率=99.00%, P95延迟=52.10ms
native-app1: 目标QPS=120, 实际QPS=119.2, 请求数=1200, 成功率=99.75%, P95延迟=38.45ms
native-app2: 目标QPS=50, 实际QPS=49.9, 请求数=500, 成功率=99.40%, P95延迟=41.67ms
```

## 性能对比

| 指标 | Python原生 | Vegeta集成 | 提升倍数 |
|------|------------|------------|----------|
| 最大QPS | ~5,000 | 50,000+ | 10x+ |
| 内存使用 | 高 | 低 | 3-5x |
| CPU使用 | 中等 | 低 | 2-3x |
| QPS精度 | 中等 | 极高 | - |
| 延迟统计 | 基础 | 详细 | - |

## 高级用法

### 1. 自定义请求头

```python
{
    "name": "custom-test",
    "url": "http://example.com/api",
    "json_file": "request.json",
    "qps": 100,
    "duration": 30,
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer token",
        "X-Custom-Header": "value"
    }
}
```

### 2. 动态修改JSON内容

如果需要动态修改JSON内容（如UUID、时间戳等），可以：

1. 创建多个JSON文件
2. 使用脚本预处理JSON文件
3. 在Python中动态生成JSON文件

### 3. 分布式测试

对于更高的QPS需求，可以在多台机器上运行：

```bash
# 机器1
python main.py vegeta

# 机器2  
python main.py vegeta

# 机器3
python main.py vegeta
```

然后汇总结果。

## 故障排除

### 1. Vegeta未安装
```
错误: 未找到Vegeta，请先安装: brew install vegeta
解决: 按照安装指南安装Vegeta
```

### 2. JSON文件不存在
```
错误: JSON文件不存在: transsion-banner-app-country.json
解决: 确保JSON文件路径正确，文件存在
```

### 3. QPS过高导致错误
```
解决: 降低QPS设置，检查目标服务器性能
```

### 4. 网络连接问题
```
解决: 检查网络连接，确认目标URL可访问
```

## 最佳实践

1. **逐步增加QPS**：从低QPS开始，逐步增加到目标值
2. **监控目标服务**：确保目标服务能承受测试压力
3. **合理设置持续时间**：避免过长时间的高压力测试
4. **定期清理**：测试完成后清理临时文件
5. **结果分析**：重点关注P95延迟和成功率指标

## 扩展开发

如需更复杂的功能，可以：

1. 修改 `vegeta_integration.py` 添加新特性
2. 使用Vegeta的Go库直接开发
3. 集成Prometheus监控
4. 添加结果可视化

## 技术支持

如遇问题，请检查：
1. Vegeta版本是否最新
2. JSON文件格式是否正确
3. 网络连接是否正常
4. 系统资源是否充足
