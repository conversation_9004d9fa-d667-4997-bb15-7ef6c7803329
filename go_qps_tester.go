package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"
)

// QpsController 精确QPS控制器
type QpsController struct {
	rate       float64
	interval   time.Duration
	bucket     chan struct{}
	ctx        context.Context
	cancel     context.CancelFunc
	lastTick   int64
	tokenCount int64
}

// NewQpsController 创建QPS控制器
func NewQpsController(qps float64) *QpsController {
	ctx, cancel := context.WithCancel(context.Background())
	
	controller := &QpsController{
		rate:     qps,
		interval: time.Duration(float64(time.Second) / qps),
		bucket:   make(chan struct{}, int(qps*2)), // 缓冲区大小为QPS的2倍
		ctx:      ctx,
		cancel:   cancel,
		lastTick: time.Now().UnixNano(),
	}
	
	// 启动令牌生成器
	go controller.tokenGenerator()
	
	return controller
}

// tokenGenerator 令牌生成器 - 使用高精度定时器
func (qc *QpsController) tokenGenerator() {
	ticker := time.NewTicker(qc.interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-qc.ctx.Done():
			return
		case <-ticker.C:
			// 非阻塞发送令牌
			select {
			case qc.bucket <- struct{}{}:
				atomic.AddInt64(&qc.tokenCount, 1)
			default:
				// 桶满了，丢弃令牌
			}
		}
	}
}

// Acquire 获取令牌
func (qc *QpsController) Acquire() {
	<-qc.bucket
	atomic.AddInt64(&qc.tokenCount, -1)
}

// Close 关闭控制器
func (qc *QpsController) Close() {
	qc.cancel()
	close(qc.bucket)
}

// RequestConfig 请求配置
type RequestConfig struct {
	Method  string            `json:"method"`
	URL     string            `json:"url"`
	Headers map[string]string `json:"headers"`
	Body    string            `json:"body"`
}

// TestResult 测试结果
type TestResult struct {
	TotalRequests   int64         `json:"total_requests"`
	SuccessRequests int64         `json:"success_requests"`
	FailedRequests  int64         `json:"failed_requests"`
	TotalDuration   time.Duration `json:"total_duration"`
	ActualQPS       float64       `json:"actual_qps"`
	AvgLatency      time.Duration `json:"avg_latency"`
	P95Latency      time.Duration `json:"p95_latency"`
	P99Latency      time.Duration `json:"p99_latency"`
	StatusCodes     map[int]int64 `json:"status_codes"`
	Errors          []string      `json:"errors"`
}

// QpsTester 高性能QPS测试器
type QpsTester struct {
	client     *http.Client
	controller *QpsController
	results    chan *RequestResult
	wg         sync.WaitGroup
}

// RequestResult 单个请求结果
type RequestResult struct {
	StatusCode int
	Latency    time.Duration
	Error      error
	Timestamp  time.Time
}

// NewQpsTester 创建QPS测试器
func NewQpsTester(qps float64, timeout time.Duration) *QpsTester {
	return &QpsTester{
		client: &http.Client{
			Timeout: timeout,
			Transport: &http.Transport{
				MaxIdleConns:        1000,
				MaxIdleConnsPerHost: 100,
				IdleConnTimeout:     90 * time.Second,
			},
		},
		controller: NewQpsController(qps),
		results:    make(chan *RequestResult, 10000),
	}
}

// RunTest 运行QPS测试
func (qt *QpsTester) RunTest(config RequestConfig, qps float64, duration time.Duration) (*TestResult, error) {
	startTime := time.Now()
	endTime := startTime.Add(duration)
	
	// 启动结果收集器
	var results []*RequestResult
	var resultsMutex sync.Mutex
	
	go func() {
		for result := range qt.results {
			resultsMutex.Lock()
			results = append(results, result)
			resultsMutex.Unlock()
		}
	}()
	
	// 启动请求发送器
	requestCount := int64(0)
	for time.Now().Before(endTime) {
		qt.controller.Acquire() // 获取令牌
		
		qt.wg.Add(1)
		go func() {
			defer qt.wg.Done()
			qt.sendRequest(config)
			atomic.AddInt64(&requestCount, 1)
		}()
	}
	
	// 等待所有请求完成
	qt.wg.Wait()
	close(qt.results)
	
	// 计算统计结果
	return qt.calculateResults(results, time.Since(startTime), requestCount), nil
}

// sendRequest 发送单个请求
func (qt *QpsTester) sendRequest(config RequestConfig) {
	start := time.Now()
	
	var body io.Reader
	if config.Body != "" {
		body = strings.NewReader(config.Body)
	}
	
	req, err := http.NewRequest(config.Method, config.URL, body)
	if err != nil {
		qt.results <- &RequestResult{
			Error:     err,
			Timestamp: start,
		}
		return
	}
	
	// 设置请求头
	for key, value := range config.Headers {
		req.Header.Set(key, value)
	}
	
	resp, err := qt.client.Do(req)
	latency := time.Since(start)
	
	result := &RequestResult{
		Latency:   latency,
		Timestamp: start,
	}
	
	if err != nil {
		result.Error = err
	} else {
		result.StatusCode = resp.StatusCode
		resp.Body.Close()
	}
	
	qt.results <- result
}

// calculateResults 计算测试结果
func (qt *QpsTester) calculateResults(results []*RequestResult, totalDuration time.Duration, requestCount int64) *TestResult {
	if len(results) == 0 {
		return &TestResult{}
	}
	
	var successCount, failedCount int64
	var totalLatency time.Duration
	statusCodes := make(map[int]int64)
	var errors []string
	var latencies []time.Duration
	
	for _, result := range results {
		if result.Error != nil {
			failedCount++
			errors = append(errors, result.Error.Error())
		} else {
			successCount++
			statusCodes[result.StatusCode]++
		}
		
		totalLatency += result.Latency
		latencies = append(latencies, result.Latency)
	}
	
	// 计算延迟百分位数
	p95, p99 := calculatePercentiles(latencies)
	
	return &TestResult{
		TotalRequests:   requestCount,
		SuccessRequests: successCount,
		FailedRequests:  failedCount,
		TotalDuration:   totalDuration,
		ActualQPS:       float64(requestCount) / totalDuration.Seconds(),
		AvgLatency:      time.Duration(int64(totalLatency) / int64(len(results))),
		P95Latency:      p95,
		P99Latency:      p99,
		StatusCodes:     statusCodes,
		Errors:          errors,
	}
}

// calculatePercentiles 计算延迟百分位数
func calculatePercentiles(latencies []time.Duration) (p95, p99 time.Duration) {
	if len(latencies) == 0 {
		return 0, 0
	}
	
	// 简单排序实现（生产环境建议使用更高效的算法）
	for i := 0; i < len(latencies)-1; i++ {
		for j := 0; j < len(latencies)-i-1; j++ {
			if latencies[j] > latencies[j+1] {
				latencies[j], latencies[j+1] = latencies[j+1], latencies[j]
			}
		}
	}
	
	p95Index := int(float64(len(latencies)) * 0.95)
	p99Index := int(float64(len(latencies)) * 0.99)
	
	if p95Index >= len(latencies) {
		p95Index = len(latencies) - 1
	}
	if p99Index >= len(latencies) {
		p99Index = len(latencies) - 1
	}
	
	return latencies[p95Index], latencies[p99Index]
}

// Close 关闭测试器
func (qt *QpsTester) Close() {
	qt.controller.Close()
}

// 并行测试示例
func main() {
	configs := []struct {
		name   string
		config RequestConfig
		qps    float64
	}{
		{
			name: "banner-app1",
			config: RequestConfig{
				Method: "POST",
				URL:    "http://172.31.236.166:8089/engine/bid/transsion",
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
				Body: `{"test": "banner-app1"}`,
			},
			qps: 50,
		},
		{
			name: "native-app1",
			config: RequestConfig{
				Method: "POST",
				URL:    "http://172.31.236.166:8089/engine/bid/transsion",
				Headers: map[string]string{
					"Content-Type": "application/json",
				},
				Body: `{"test": "native-app1"}`,
			},
			qps: 100,
		},
	}
	
	duration := 10 * time.Second
	timeout := 5 * time.Second
	
	fmt.Println("开始并行QPS测试...")
	start := time.Now()
	
	var wg sync.WaitGroup
	results := make(chan struct {
		name   string
		result *TestResult
	}, len(configs))
	
	// 并行执行测试
	for _, cfg := range configs {
		wg.Add(1)
		go func(name string, config RequestConfig, qps float64) {
			defer wg.Done()
			
			tester := NewQpsTester(qps, timeout)
			defer tester.Close()
			
			result, err := tester.RunTest(config, qps, duration)
			if err != nil {
				fmt.Printf("测试 %s 失败: %v\n", name, err)
				return
			}
			
			results <- struct {
				name   string
				result *TestResult
			}{name, result}
		}(cfg.name, cfg.config, cfg.qps)
	}
	
	// 等待所有测试完成
	go func() {
		wg.Wait()
		close(results)
	}()
	
	// 收集结果
	var totalRequests int64
	var totalQPS float64
	
	for result := range results {
		fmt.Printf("%s: QPS=%.2f, 成功率=%.2f%%, 平均延迟=%v\n",
			result.name,
			result.result.ActualQPS,
			float64(result.result.SuccessRequests)/float64(result.result.TotalRequests)*100,
			result.result.AvgLatency)
		
		totalRequests += result.result.TotalRequests
		totalQPS += result.result.ActualQPS
	}
	
	totalDuration := time.Since(start)
	fmt.Printf("\n=== 总体统计 ===\n")
	fmt.Printf("总耗时: %v\n", totalDuration)
	fmt.Printf("总请求数: %d\n", totalRequests)
	fmt.Printf("实际总QPS: %.2f\n", totalQPS)
}
