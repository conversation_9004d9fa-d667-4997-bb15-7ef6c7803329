{"id": "test-banner-request-001", "imp": [{"id": "1", "banner": {"w": 690, "h": 1035, "format": [{"w": 690, "h": 1035}]}, "displaymanager": "transsion", "displaymanagerver": "1.0", "tagid": "banner_test", "bidfloor": 0.01, "bidfloorcur": "USD"}], "app": {"id": "test_app_id", "name": "Test App", "bundle": "com.transsion.XOSLauncher", "domain": "transsion.com", "cat": ["IAB1"], "ver": "1.0.0", "publisher": {"id": "test_publisher", "name": "Test Publisher"}}, "device": {"ua": "Mozilla/5.0 (Linux; Android 10; SM-G975F) AppleWebKit/537.36", "geo": {"lat": 37.7749, "lon": -122.4194, "country": "USA", "region": "CA", "city": "San Francisco"}, "ip": "***************", "devicetype": 1, "make": "Samsung", "model": "SM-G975F", "os": "Android", "osv": "10", "w": 1080, "h": 2340, "ppi": 550, "language": "en", "carrier": "Verizon", "connectiontype": 2, "ifa": "test-advertising-id"}, "user": {"id": "test_user_id", "yob": 1990, "gender": "M"}, "test": 1, "at": 2, "tmax": 120, "cur": ["USD"], "bcat": ["IAB25", "IAB26"], "badv": ["example-blocked-advertiser.com"]}