#!/bin/bash

# Go原生QPS测试工具运行脚本

set -e

echo "🚀 Go原生高性能QPS测试工具"
echo "================================"

# 检查是否已构建
if [ ! -f "qps-tester" ]; then
    echo "📦 首次运行，正在构建..."
    ./build.sh
fi

# 检查JSON文件
echo "🔍 检查JSON文件..."
missing_files=()

for file in "banner-app1.json" "banner-app2.json" "native-app1.json" "native-app2.json"; do
    if [ ! -f "$file" ]; then
        missing_files+=("$file")
    else
        echo "✅ $file"
    fi
done

if [ ${#missing_files[@]} -gt 0 ]; then
    echo "❌ 缺少以下JSON文件:"
    for file in "${missing_files[@]}"; do
        echo "   - $file"
    done
    echo ""
    echo "请创建这些文件或使用现有的JSON文件"
    exit 1
fi

echo ""
echo "🎯 开始执行QPS测试..."
echo "================================"

# 运行测试
./qps-tester

echo ""
echo "✅ 测试完成！"
